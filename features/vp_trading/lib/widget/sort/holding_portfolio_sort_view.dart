import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/widget/sort/holding_portfolio_sort_cubit.dart';

class HoldingPortfolioSortView extends StatelessWidget {
  const HoldingPortfolioSortView({
    required this.builder,
    this.onChanged,
    this.status,
    this.sortType,
    this.showProfitLossPercent = false,
    super.key,
  });

  final SortStatus? status;

  final StockInfoFieldType? sortType;

  final bool showProfitLossPercent;

  final void Function(BuildContext context, SortMode mode)? onChanged;

  final Widget Function(
    BuildContext context,
    List<SortColumn> columns,
    bool showProfitLossPercent,
  )
  builder;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<HoldingPortfolioSortCubit>(
      create:
          (_) => HoldingPortfolioSortCubit(
            status: status,
            sortType: sortType,
            showProfitLossPercent: showProfitLossPercent,
            columns: [
              SortColumn(
                title: VPTradingLocalize.current.trading_stock_code,
                sortType: StockInfoFieldType.symbol,
                align: MainAxisAlignment.start,
                flex: 5,
                id: 0,
              ),
              SortColumn(
                title: VPTradingLocalize.current.trading_cost_price,
                sortType: StockInfoFieldType.closePrice,
                flex: 4,
                id: 1,
              ),
              SortColumn(
                title: VPTradingLocalize.current.trading_volume_title,
                sortType: StockInfoFieldType.vol,
                flex: 4,
                id: 2,
              ),
              GroupColumn(
                id: 3,
                columns: [
                  SortColumn(
                    title:
                        VPTradingLocalize.current.trading_expected_profit_loss,
                    sortType: StockInfoFieldType.profitLoss,
                    flex: 5,
                    id: 4,
                    groupId: 3,
                  ),
                  SortColumn(
                    title:
                        VPTradingLocalize.current.trading_profit_loss_percent,
                    sortType: StockInfoFieldType.profitLossPercent,
                    flex: 5,
                    id: 5,
                    groupId: 3,
                  ),
                ],
              ),
            ],
          ),
      child: HoldingPortfolioSortContentView(
        builder: builder,
        onChanged: onChanged,
      ),
    );
  }
}

class HoldingPortfolioSortContentView extends StatelessWidget {
  const HoldingPortfolioSortContentView({
    required this.builder,
    this.onChanged,
    super.key,
  });

  final void Function(BuildContext context, SortMode mode)? onChanged;

  final Widget Function(
    BuildContext context,
    List<SortColumn> columns,
    bool showProfitLossPercent,
  )
  builder;

  @override
  Widget build(BuildContext context) {
    return BlocListener<HoldingPortfolioSortCubit, HoldingPortfolioSortState>(
      listenWhen:
          (preState, state) =>
              preState.status != state.status ||
              preState.sortType != state.sortType,
      listener: (context, state) {
        if (state.sortType != null) {
          onChanged?.call(
            context,
            SortMode(sortType: state.sortType!, status: state.status),
          );
        }
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 16, 5),
            child: BlocBuilder<
              HoldingPortfolioSortCubit,
              HoldingPortfolioSortState
            >(
              buildWhen: (_, __) => false,
              builder: (_, state) {
                return Row(
                  children: [
                    ...state.columns.map((e) {
                      return switch (e) {
                        SortColumn() => Expanded(
                          flex: e.flex,
                          child: HoldingPortfolioSortItemView(column: e),
                        ),
                        GroupColumn() => Expanded(
                          flex: state.getCurrentColumn(e).flex,
                          child: HoldingPortfolioSortGroupItemView(group: e),
                        ),
                      };
                    }),
                    // Add expand/collapse button
                    _buildExpandCollapseButton(context, state),
                  ],
                );
              },
            ),
          ),
          Expanded(
            child: BlocBuilder<
              HoldingPortfolioSortCubit,
              HoldingPortfolioSortState
            >(
              builder: (_, state) {
                return builder(
                  context,
                  state.visibleColumns,
                  state.showProfitLossPercent,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandCollapseButton(
    BuildContext context,
    HoldingPortfolioSortState state,
  ) {
    return GestureDetector(
      onTap:
          () =>
              context
                  .read<HoldingPortfolioSortCubit>()
                  .toggleProfitLossPercentColumn(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Icon(
          state.showProfitLossPercent
              ? Icons.keyboard_arrow_left
              : Icons.keyboard_arrow_right,
          size: 16,
          color: vpColor.textTertiary,
        ),
      ),
    );
  }
}

class HoldingPortfolioSortItemView extends StatelessWidget {
  const HoldingPortfolioSortItemView({required this.column, super.key});

  final SortColumn column;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () => context.read<HoldingPortfolioSortCubit>().onSortChange(
            column.sortType,
          ),
      child: ColoredBox(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 3),
          child: Row(
            mainAxisAlignment: column.align,
            children: [
              Text(
                column.title,
                style: vpTextStyle.captionRegular.copyColor(
                  vpColor.textTertiary,
                ),
              ),
              const SizedBox(width: 2),
              Column(
                children: [_buildIncreaseIconView(), _buildDecreaseIconView()],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIncreaseIconView() {
    return BlocBuilder<HoldingPortfolioSortCubit, HoldingPortfolioSortState>(
      builder: (_, state) {
        final status = state.status;
        final sortType = state.sortType;

        final isIncrease = column.sortType == sortType && status.isIncrease;

        return Assets.icons.icSortIncrease.svg(
          height: 4,
          colorFilter:
              isIncrease
                  ? ColorFilter.mode(vpColor.textPriceGreen, BlendMode.srcIn)
                  : null,
        );
      },
    );
  }

  Widget _buildDecreaseIconView() {
    return BlocBuilder<HoldingPortfolioSortCubit, HoldingPortfolioSortState>(
      builder: (_, state) {
        final status = state.status;
        final sortType = state.sortType;

        final isDecrease = column.sortType == sortType && status.isDecrease;

        return Assets.icons.icSortDecrease.svg(
          height: 4,
          color: isDecrease ? vpColor.textPriceGreen : null,
        );
      },
    );
  }
}

class HoldingPortfolioSortGroupItemView extends StatelessWidget {
  const HoldingPortfolioSortGroupItemView({required this.group, super.key});

  final GroupColumn group;

  void onSortChange(BuildContext context) {
    final cubit = context.read<HoldingPortfolioSortCubit>();
    final currentColumn = cubit.state.getCurrentColumn(group);
    cubit.onSortChange(currentColumn.sortType);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap:
              () => context.read<HoldingPortfolioSortCubit>().onPrevious(group),
          child: Assets.icons.icArrowLeft.svg(),
        ),
        GestureDetector(
          onTap: () => onSortChange(context),
          child: ColoredBox(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(5, 3, 5, 3),
              child: Stack(
                clipBehavior: Clip.none,
                alignment: Alignment.center,
                children: [
                  BlocBuilder<
                    HoldingPortfolioSortCubit,
                    HoldingPortfolioSortState
                  >(
                    builder: (context, state) {
                      return Text(
                        state.getCurrentColumn(group).title,
                        style: vpTextStyle.captionRegular.copyColor(
                          vpColor.textTertiary,
                        ),
                      );
                    },
                  ),
                  _buildIconView(),
                ],
              ),
            ),
          ),
        ),
        GestureDetector(
          onTap: () => context.read<HoldingPortfolioSortCubit>().onNext(group),
          child: Assets.icons.icArrowRight.svg(),
        ),
      ],
    );
  }

  Widget _buildIconView() {
    return BlocBuilder<HoldingPortfolioSortCubit, HoldingPortfolioSortState>(
      builder: (_, state) {
        final status = state.status;
        final sortType = state.sortType;

        final showIcon = group.columns.any((e) => e.sortType == sortType);

        if (showIcon && status.isIncrease) {
          return Positioned(
            bottom: -10,
            child: Assets.icons.icSortIncrease.svg(color: vpColor.textTertiary),
          );
        }

        if (showIcon && status.isDecrease) {
          return Positioned(
            bottom: -10,
            child: Assets.icons.icSortDecrease.svg(color: vpColor.textTertiary),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
